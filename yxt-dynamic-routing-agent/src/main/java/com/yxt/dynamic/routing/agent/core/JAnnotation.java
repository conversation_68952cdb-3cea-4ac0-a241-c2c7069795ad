package com.yxt.dynamic.routing.agent.core;

import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.annotation.Annotation;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/21 18:01
 */
public class JAnnotation {
    private String annName;
    private JClass jClass;

    private boolean remove = false;

    public JAnnotation(JClass jClass, String annName) {
        this.jClass = jClass;
        this.annName = annName;
    }

    public JClass then() {
        return this.jClass;
    }

    public JAnnotation remove() {
        remove = true;
        return this;
    }

    protected void removeClassApply(AnnotationsAttribute newAnnotationsAttr) {
        if(!remove){
            return;
        }
        // 过滤掉特定注解
        for (Annotation annotation : newAnnotationsAttr.getAnnotations()) {
            if (annName.equals(annotation.getTypeName())) {
                newAnnotationsAttr.removeAnnotation(annName);
            }
        }
    }

}