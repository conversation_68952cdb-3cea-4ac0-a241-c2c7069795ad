package com.yxt.dynamic.routing.agent.core;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.security.ProtectionDomain;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/23 13:49
 */
public class WriteClassComponent {

    List<WriteDomain> list = new ArrayList<>();
    private File targetRootFolder;
    private JSpring jSpring;
    public void write(byte[] bytecode, JClass jClass, ProtectionDomain protectionDomain) throws IOException {
        list.add(new WriteDomain(bytecode, jClass));

        if (jClass.isRoot()) {
            String file = protectionDomain.getCodeSource().getLocation().getFile();
            String folderPath = file.substring(1, file.length() - 1);
            targetRootFolder = new File(folderPath);
            if(jSpring!=null){
                byte[] beanExclusionPostProcessorClass = jSpring.createBeanExclusionPostProcessorClass(jClass.getPackageName());
                if(beanExclusionPostProcessorClass!=null){
                    FileUtils.writeByteArrayToFile(new File(targetRootFolder,jClass.getRelativePath()+jSpring.getFileName()),beanExclusionPostProcessorClass);
                }
            }
            write(targetRootFolder,list);
        }
        write(targetRootFolder,list);
    }

    private void write(File targetRootFolder, List<WriteDomain> list) throws IOException {
        ListIterator<WriteDomain> iterator = list.listIterator();
        while (iterator.hasNext() && targetRootFolder != null) {
            WriteDomain next = iterator.next();
            String loadClassName = next.getjClass().getLoadClassName();
            File file = new File(targetRootFolder, loadClassName + ".class");
            FileUtils.writeByteArrayToFile(file, next.getBytecode());
            iterator.remove();
        }
    }

    public void writeJSpring(JSpring jSpring){
        this.jSpring = jSpring;
    }

    public static class WriteDomain {
        public WriteDomain(byte[] bytecode, JClass jClass) {
            this.bytecode = bytecode;
            this.jClass = jClass;
        }

        private byte[] bytecode;
        private JClass jClass;

        public byte[] getBytecode() {
            return bytecode;
        }

        public JClass getjClass() {
            return jClass;
        }
    }

}
