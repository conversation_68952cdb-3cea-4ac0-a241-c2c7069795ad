package com.yxt.dynamic.routing.agent;

import com.yxt.dynamic.routing.agent.core.JTransformer;

import java.lang.instrument.Instrumentation;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/23 11:55
 */
public class Main {

    public static void premain(String agentArgs, Instrumentation instrumentation) {
        System.out.println("dynamic start");
        JTransformer jTransformer = JTransformer.create();
        jTransformer
//                .jClass("cn.hydee.middle.business.order.BusinessOrderApplication").root().then()
                //okHttp---------ok
                .jClass("okhttp3.OkHttpClient$Builder")
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("build")
                    .insertBefore(protectWrap("okhttp3.Interceptor o ="+getInstance("com.yxt.dynamic.routing.proxy.DynamicOkHttpInterceptor","getInstance")+";$0.addInterceptor(o);")).then().then()
                //rocketMq
                .jClass("org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest")
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("run")
                    .insertBefore(protectWrap(getInstanceAndInvoke("com.yxt.dynamic.routing.proxy.DynamicRocketMQInterceptor","getInstance","interceptReceive","$0.msgs")))
                    .insertAfter(staticInvoke("com.yxt.dynamic.routing.proxy.DynamicRouteContext","clear"),true).then().then()
                .jClass("org.apache.rocketmq.client.producer.DefaultMQProducer")
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("send")
                    .insertAllBefore(mqInterceptSend()).then()
                    .jMethod("sendOneway")
                    .insertAllBefore(mqInterceptSend()).then()
                    .jMethod("request")
                    .insertAllBefore(mqInterceptSend()).then().then()
                .jClass("org.apache.rocketmq.client.producer.TransactionMQProducer")
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("sendMessageInTransaction")
                    .insertAllBefore(mqInterceptSend()).then().then()
//                //线程池
                .jClass("java.util.concurrent.ThreadPoolExecutor")//测试通过
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("execute","java.lang.Runnable")
                    .insertBefore(threadWrap()).then().then()
                .jClass("java.util.concurrent.AbstractExecutorService")//测试通过
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("submit")
                    .insertAllBefore(threadWrap()).then().then()
                .jClass("org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor")//测试通过
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("execute")
                    .insertAllBefore(threadWrap()).then()
                    .jMethod("submit")
                    .insertAllBefore(threadWrap()).then()
                    .jMethod("submitListenable")
                    .insertAllBefore(threadWrap()).then().then()
                .jClass("java.util.concurrent.ForkJoinPool")//测试通过
                    .jField("private static boolean dynasticNotUse = false;").then()
                    .jMethod("submit")
                    .insertAllBefore(threadWrap()).then()
                    .jMethod("execute")
                    .insertAllBefore(threadWrap()).then().then();
        instrumentation.addTransformer(jTransformer);
    }

    private static String threadWrap(){
        return protectWrap("$1 = Class.forName(\"com.yxt.dynamic.routing.proxy.ThreadWrapUtil\",true, Thread.currentThread().getContextClassLoader()).getMethod(\"wrap\",new Class[]{Object.class}).invoke(null,new Object[]{$1});");
    }

    private static String staticInvoke(String className,String method){
        return protectWrap("Class.forName(\""+className+"\",true, Thread.currentThread().getContextClassLoader()).getMethod(\""+method+"\",new Class[0]).invoke(null,new Object[0]);");
    }

    private static String protectWrap(String method){
        return "try{if(!$0.dynasticNotUse){"+method+"}}catch (Exception e){$0.dynasticNotUse = true;}";
    }

    private static String mqInterceptSend(){
        return protectWrap(getInstanceAndInvoke("com.yxt.dynamic.routing.proxy.DynamicRocketMQInterceptor","getInstance","interceptSend","$1"));
    }

    private static String getInstanceAndInvoke(String className,String instanceMethod,String invokeName,String params){
        return "         Class c = Class.forName(\""+className+"\",true, Thread.currentThread().getContextClassLoader());\n"+
                "        java.lang.Object o = c.getMethod(\""+instanceMethod+"\",new Class[0]).invoke(null,new Class[0]);\n" +
                "        c.getMethod(\""+invokeName+"\",new Class[]{Object.class}).invoke(o,new Object[]{"+params+"});";
    }

    private static String getInstance(String className,String instanceMethod){
        return "Class.forName(\""+className+"\",true, Thread.currentThread().getContextClassLoader()).getMethod(\""+instanceMethod+"\",new Class[0]).invoke(null,new Class[0]);";
    }

}
