package com.yxt.dynamic.routing.web.starter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientConfiguration;
import org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration;
import org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration;
import org.springframework.context.annotation.Configuration;

/**
 * Created by peng on 19/12/5.
 */
@Slf4j
@AutoConfigureAfter(LoadBalancerAutoConfiguration.class)
@AutoConfigureBefore(value = {BlockingLoadBalancerClientAutoConfiguration.class,
    LoadBalancerClientConfiguration.class})
@ConditionalOnProperty(value = "grey.enable", havingValue = "true", matchIfMissing = true)
@Configuration
//@Import(value = {GreyRocketMQWebConfiguration.class})
public class SpringBootGreyWebAutoConfiguration {

}
