package com.yxt.dynamic.routing.proxy;

import org.slf4j.MDC;

import java.util.concurrent.Callable;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 16:57
 */
public class MDCCallable implements Callable<Object>{
    private String routes;
    private String mockKey;
    private Callable<Object> callable;

    public MDCCallable(Callable<Object> callable) {
        routes = MDC.get(DynamicRouteContext.ROUTE_KEY);
        this.mockKey = MDC.get(DynamicRouteContext.MOCK_KEY);
        this.callable = callable;
    }

    @Override
    public Object call() throws Exception {
        if (routes != null) {
            MDC.put(DynamicRouteContext.ROUTE_KEY, routes);
        }
        if (mockKey != null) {
            MDC.put(DynamicRouteContext.MOCK_KEY, mockKey);
        }
        try {
            return callable.call();
        } finally {
            MDC.clear();
        }
    }
}
