package com.yxt.dynamic.routing.proxy;

import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.RunnableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 16:57
 */
public class MDCFutureTask<V> implements RunnableFuture<V> {
    private String routes;
    private String mockKey;
    private FutureTask<V> futureTask;

    public MDCFutureTask(FutureTask futureTask) {
        this.routes = MDC.get(DynamicRouteContext.ROUTE_KEY);
        this.mockKey = MDC.get(DynamicRouteContext.MOCK_KEY);
        this.futureTask = futureTask;
    }

    @Override
    public void run() {
        if (routes != null) {
            MDC.put(DynamicRouteContext.ROUTE_KEY, routes);
        }
        if (mockKey != null) {
            MDC.put(DynamicRouteContext.MOCK_KEY, mockKey);
        }
        try {
            futureTask.run();
        } finally {
            MDC.clear();
        }
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return futureTask.cancel(mayInterruptIfRunning);
    }

    @Override
    public boolean isCancelled() {
        return futureTask.isCancelled();
    }

    @Override
    public boolean isDone() {
        return futureTask.isDone();
    }

    @Override
    public V get() throws InterruptedException, ExecutionException {
        return futureTask.get();
    }

    @Override
    public V get(long timeout, @NotNull TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return futureTask.get(timeout, unit);
    }
}
