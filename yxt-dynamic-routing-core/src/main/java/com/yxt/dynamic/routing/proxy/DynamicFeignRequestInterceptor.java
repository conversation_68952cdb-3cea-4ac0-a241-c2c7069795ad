package com.yxt.dynamic.routing.proxy;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 17:30
 */
public class DynamicFeignRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        String allRoute = DynamicRouteContext.getAllRoute();
        String mockKey = DynamicRouteContext.getMockKey();
        String oriPath = DynamicRouteContext.getOriHost();
        if(StringUtils.isNotBlank(allRoute)){
            template.header(DynamicRouteContext.ROUTE_KEY,allRoute);
        }
        if(StringUtils.isNotBlank(mockKey)){
            template.header(DynamicRouteContext.MOCK_KEY,mockKey);
        }
        if(StringUtils.isNotBlank(oriPath)){
            template.header(DynamicRouteContext.ORI_HOST_KEY,oriPath);
        }
    }
}
