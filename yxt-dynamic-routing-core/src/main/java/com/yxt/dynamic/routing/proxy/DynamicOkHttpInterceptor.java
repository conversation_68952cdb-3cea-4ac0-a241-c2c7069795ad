package com.yxt.dynamic.routing.proxy;

import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.client.utils.URIUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/20 15:38
 */
@Slf4j
public class DynamicOkHttpInterceptor implements Interceptor {
    private static DynamicOkHttpInterceptor instance;
    private NacosDiscoveryClient nacosDiscoveryClient;
    private DynamicProperties dynamicProperties;

    public static DynamicOkHttpInterceptor getInstance() {
        if (instance == null) {
            synchronized (DynamicOkHttpInterceptor.class) {
                if (instance == null) {
                    instance = new DynamicOkHttpInterceptor();
                }
            }
        }
        return instance;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        String allRoute = DynamicRouteContext.getAllRoute();
        if (StringUtils.isBlank(allRoute)) {
            return chain.proceed(chain.request());
        }
        dynamicProperties = getDynamicProperties();
        if (dynamicProperties == null || !dynamicProperties.isEnable()) {
            return chain.proceed(chain.request());
        }
        String mockKey = DynamicRouteContext.getMockKey();
        String oldUrl = chain.request().url().toString();
        Request.Builder builder = chain.request().newBuilder();
        builder.url(route(oldUrl))
                .addHeader(DynamicRouteContext.ROUTE_KEY, allRoute);
        if(StringUtils.isNotBlank(mockKey)){
            builder.addHeader(DynamicRouteContext.MOCK_KEY, mockKey).build();
        }
        if(StringUtils.isNotBlank(DynamicRouteContext.getOriHost())){
            builder.addHeader(DynamicRouteContext.ORI_HOST_KEY, DynamicRouteContext.getOriHost()).build();
        }
        Request newRequest = builder.build();
        return chain.proceed(newRequest);
    }

    public String route(String requestUrl) {
        setOriHost(requestUrl);
        try {
            Map.Entry<String, String> entry = DynamicRouteContext.matchRoute(requestUrl);
            if (entry == null || StringUtils.isBlank(entry.getValue()) || requestUrl.equals(entry.getValue())) {
                return requestUrl;
            }
            log.info("动态路由 原始地址：{} 路由地址：{}",requestUrl,entry.getValue());
            if (entry.getValue().startsWith("http") || entry.getValue().startsWith("https")) {
                return requestUrl.replaceAll(entry.getKey(), entry.getValue());
            }
            if (nacosDiscoveryClient() != null) {
                List<ServiceInstance> instances = nacosDiscoveryClient().getInstances(entry.getValue());
                if (CollectionUtils.isEmpty(instances)) {
                    log.warn("动态路由 nacos中没有对应服务 url:{} serverName:{} route:{}", requestUrl, entry.getValue(), DynamicRouteContext.getAllRoute());
                    return requestUrl;
                }
                ServiceInstance serviceInstance = instances.get(0);
                URI uri = URIUtils.rewriteURI(new URI(requestUrl), new HttpHost(serviceInstance.getHost(), serviceInstance.getPort()));
                return uri.toString();
            }
        } catch (Exception e) {
            log.error("动态路由 执行错误", e);
        }
        return requestUrl;
    }

    private void setOriHost(String requestUrl) {
        String serverName = DynamicRouteContext.matchServerName(requestUrl);
        if(StringUtils.isNotBlank(serverName)){
            DynamicRouteContext.setOriHost(serverName);
        }else {
            DynamicRouteContext.setOriHost(requestUrl);
        }
    }

    private DynamicProperties getDynamicProperties() {
        if (dynamicProperties == null) {
            dynamicProperties = DynamicBeanComponent.getBean(DynamicProperties.class);
        }
        return dynamicProperties;
    }

    private NacosDiscoveryClient nacosDiscoveryClient() {
        if (nacosDiscoveryClient == null) {
            nacosDiscoveryClient = DynamicBeanComponent.getBean(NacosDiscoveryClient.class);
        }
        return nacosDiscoveryClient;
    }

}
