package com.yxt.dynamic.routing.proxy;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description: 用于动态路由，httpClient发起的请求时动态修改host
 * 该注解放在获取三方请求url的方法上即可
 *
 * @DynamicRoute
 * public String getUrl(){
 *     return url;
 * }
 *
 * <AUTHOR>
 * @Date 2024/9/11 17:10
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(value = ElementType.METHOD)
public @interface DynamicRoute {
}
