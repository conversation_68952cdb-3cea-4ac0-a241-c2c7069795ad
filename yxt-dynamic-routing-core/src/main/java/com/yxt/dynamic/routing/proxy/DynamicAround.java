package com.yxt.dynamic.routing.proxy;

import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.client.utils.URIUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;

import java.net.URI;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 16:58
 */
@Aspect
@Slf4j
public class DynamicAround {
    @Autowired
    @Lazy
    NacosDiscoveryClient nacosDiscoveryClient;
    @Around("@annotation(com.yxt.dynamic.routing.proxy.DynamicRoute)")
    public Object dynamic(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed = joinPoint.proceed(joinPoint.getArgs());
        if(proceed==null){
            return proceed;
        }
        if(proceed instanceof String){
            String url = (String) proceed;
            if(StringUtils.isBlank(DynamicRouteContext.getAllRoute())){
                return proceed;
            }
            Map.Entry<String, String> entry = DynamicRouteContext.matchRoute(url);
            if(entry==null || StringUtils.isBlank(entry.getValue()) || proceed.equals(entry.getValue())){
                log.error("动态路由 没有匹配上 url:{} route:{}",url,DynamicRouteContext.getAllRoute());
                return proceed;
            }
            if(entry.getValue().startsWith("http")){
                return url.replaceAll(entry.getKey(),entry.getValue());
            }
            List<ServiceInstance> instances = nacosDiscoveryClient.getInstances(entry.getValue());
            if(CollectionUtils.isEmpty(instances)){
                log.error("动态路由 nacos中没有对应服务 url:{} appName:{} route:{}",url,entry.getValue(),DynamicRouteContext.getAllRoute());
                return proceed;
            }
            ServiceInstance serviceInstance = instances.get(0);
            URI uri = URIUtils.rewriteURI(new URI(url), new HttpHost(serviceInstance.getHost(), serviceInstance.getPort()));
            return uri.toString();
        }else {
            log.error("动态路由 Dynamic使用错误，请放到返回值为String的方法上");
            return proceed;
        }
    }

//  替换成javaassist方式进行修改
//
//    @Around("execution(public * org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor.execute(..))")
//    public Object execute(ProceedingJoinPoint joinPoint) throws Throwable {
//        return joinPoint.proceed(wrap(joinPoint.getArgs()));
//    }
//
//    @Around("execution(public * org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor.submit(..))")
//    public Object submit(ProceedingJoinPoint joinPoint) throws Throwable {
//        return joinPoint.proceed(wrap(joinPoint.getArgs()));
//    }
//
//    @Around("execution(* java.util.concurrent.AbstractExecutorService+.submit(..))")
//    public Object abstractExecutorServiceSubmit(ProceedingJoinPoint joinPoint) throws Throwable {
//        return joinPoint.proceed(wrap(joinPoint.getArgs()));
//    }
//
//    @Around("execution(* java.util.concurrent.ThreadPoolExecutor+.execute(..))")
//    public Object threadPoolExecutorExecute(ProceedingJoinPoint joinPoint) throws Throwable {
//        return joinPoint.proceed(wrap(joinPoint.getArgs()));
//    }

    private Object[] wrap(Object[] args) throws SQLException {
        if(args[0]==null || args.length==0){
            return args;
        }
        if(args[0] instanceof Callable){
            Callable arg = (Callable) args[0];
            MDCCallable callable = new MDCCallable(arg);
            args[0] = callable;
        }else if(args[0] instanceof Runnable) {
            Runnable arg = (Runnable) args[0];
            MDCRunnable callable = new MDCRunnable(arg);
            args[0] = callable;
        }
        return args;
    }
}
